{application,tailwind,
             [{modules,['Elixir.Mix.Tasks.Tailwind',
                        'Elixir.Mix.Tasks.Tailwind.Install',
                        'Elixir.Tailwind']},
              {optional_applications,[inets,ssl]},
              {applications,[kernel,stdlib,elixir,logger,inets,ssl]},
              {description,"Mix tasks for installing and invoking tailwind"},
              {registered,[]},
              {vsn,"0.4.0"},
              {mod,{'Elixir.Tailwind',[]}},
              {env,[{default,[]}]}]}.
