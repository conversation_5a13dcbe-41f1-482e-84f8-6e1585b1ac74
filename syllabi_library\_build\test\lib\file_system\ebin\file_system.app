{application,file_system,
             [{modules,['Elixir.FileSystem','Elixir.FileSystem.Backend',
                        'Elixir.FileSystem.Backends.FSInotify',
                        'Elixir.FileSystem.Backends.FSMac',
                        'Elixir.FileSystem.Backends.FSPoll',
                        'Elixir.FileSystem.Backends.FSWindows',
                        'Elixir.FileSystem.Worker']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger]},
              {description,"An Elixir file system change watcher wrapper based on FS, the native file\nsystem listener.\n"},
              {registered,[]},
              {vsn,"1.1.1"}]}.
