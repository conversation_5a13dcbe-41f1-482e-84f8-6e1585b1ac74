defmodule SyllabiLibrary.Repo.Migrations.CreateTenantsTable do
  use Ecto.Migration

  def change do
    # Tenants table (Organizations)
    create table(:tenants, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :name, :string, null: false  # "University ABC"
      add :slug, :string, null: false  # "university-abc" for subdomain
      add :subdomain, :string, null: false  # "university-abc.syllabi-library.com"
      add :contact_email, :string  # Admin contact for approvals
      add :contact_name, :string
      add :settings, :map, default: %{}
      add :status, :string, default: "active"  # active, suspended, pending

      timestamps(type: :utc_datetime)
    end

    create unique_index(:tenants, [:slug])
    create unique_index(:tenants, [:subdomain])

    # User-Tenant associations with approval workflow
    create table(:user_tenants, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :user_id, references(:users, on_delete: :delete_all, type: :binary_id), null: false
      add :tenant_id, references(:tenants, on_delete: :delete_all, type: :binary_id), null: false
      add :role, :string, default: "member"  # admin, member
      add :status, :string, default: "pending"  # pending, approved, rejected, revoked
      add :requested_at, :utc_datetime, null: false, default: fragment("NOW()")
      add :approved_at, :utc_datetime
      add :approved_by, references(:users, type: :binary_id)  # Admin who approved
      add :rejection_reason, :text

      timestamps(type: :utc_datetime)
    end

    create unique_index(:user_tenants, [:user_id, :tenant_id])

    # User approval requests log
    create table(:user_approval_requests, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :user_tenant_id, references(:user_tenants, on_delete: :delete_all, type: :binary_id), null: false
      add :user_id, references(:users, type: :binary_id), null: false
      add :tenant_id, references(:tenants, type: :binary_id), null: false
      add :request_type, :string, null: false  # join_request, role_change, reactivation
      add :requested_role, :string
      add :justification, :text  # User's reason for requesting access
      add :status, :string, default: "pending"  # pending, approved, rejected
      add :processed_at, :utc_datetime
      add :processed_by, references(:users, type: :binary_id)

      timestamps(type: :utc_datetime)
    end
  end
end
