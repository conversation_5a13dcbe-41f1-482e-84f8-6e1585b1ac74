defmodule SyllabiLibrary.Accounts.TenantTest do
  use SyllabiLibrary.DataCase

  alias SyllabiLibrary.Accounts.Tenant

  describe "changeset/2" do
    @valid_attrs %{
      name: "University ABC",
      slug: "university-abc",
      contact_email: "<EMAIL>"
    }

    @invalid_attrs %{name: "", slug: ""}

    test "changeset with valid attributes" do
      changeset = Tenant.changeset(%Tenant{}, @valid_attrs)
      assert changeset.valid?
      assert get_field(changeset, :subdomain) == "university-abc.syllabi-library.com"
    end

    test "changeset with invalid attributes" do
      changeset = Tenant.changeset(%Tenant{}, @invalid_attrs)
      refute changeset.valid?
      assert %{name: ["can't be blank"], slug: ["can't be blank"]} = errors_on(changeset)
    end

    test "changeset validates slug format" do
      invalid_slug_attrs = %{@valid_attrs | slug: "Invalid Slug!"}
      changeset = Tenant.changeset(%Tenant{}, invalid_slug_attrs)
      refute changeset.valid?
      assert %{slug: ["must contain only lowercase letters, numbers, and hyphens"]} = errors_on(changeset)
    end

    test "changeset validates slug length" do
      short_slug_attrs = %{@valid_attrs | slug: "ab"}
      changeset = Tenant.changeset(%Tenant{}, short_slug_attrs)
      refute changeset.valid?
      assert %{slug: ["should be at least 3 character(s)"]} = errors_on(changeset)

      long_slug_attrs = %{@valid_attrs | slug: String.duplicate("a", 51)}
      changeset = Tenant.changeset(%Tenant{}, long_slug_attrs)
      refute changeset.valid?
      assert %{slug: ["should be at most 50 character(s)"]} = errors_on(changeset)
    end

    test "changeset validates email format" do
      invalid_email_attrs = %{@valid_attrs | contact_email: "invalid_email"}
      changeset = Tenant.changeset(%Tenant{}, invalid_email_attrs)
      refute changeset.valid?
      assert %{contact_email: ["must be a valid email"]} = errors_on(changeset)
    end

    test "changeset generates subdomain from slug" do
      attrs = %{@valid_attrs | slug: "test-university"}
      changeset = Tenant.changeset(%Tenant{}, attrs)
      assert get_field(changeset, :subdomain) == "test-university.syllabi-library.com"
    end

    test "changeset allows nil contact_email" do
      attrs = Map.delete(@valid_attrs, :contact_email)
      changeset = Tenant.changeset(%Tenant{}, attrs)
      assert changeset.valid?
    end
  end

  describe "update_changeset/2" do
    test "update changeset with valid attributes" do
      tenant = %Tenant{name: "Old Name", slug: "old-slug"}
      attrs = %{name: "New Name", contact_email: "<EMAIL>"}
      changeset = Tenant.update_changeset(tenant, attrs)

      assert changeset.valid?
      assert get_field(changeset, :name) == "New Name"
      assert get_field(changeset, :contact_email) == "<EMAIL>"
      # Slug should not be updated in update changeset
      assert get_field(changeset, :slug) == "old-slug"
    end

    test "update changeset validates required name" do
      tenant = %Tenant{}
      changeset = Tenant.update_changeset(tenant, %{name: ""})
      refute changeset.valid?
      assert %{name: ["can't be blank"]} = errors_on(changeset)
    end
  end
end