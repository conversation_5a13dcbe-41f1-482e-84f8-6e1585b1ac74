version: '3.8'

services:
  # Phoenix Application
  app:
    build: .
    ports:
      - "4000:4000"
    volumes:
      # Mount source code as volume for development
      - .:/app
      - /app/deps
      - /app/_build
      - /app/assets/node_modules
    depends_on:
      - postgres
      - couchdb
    environment:
      - MIX_ENV=dev
      - DATABASE_URL=****************************************************/syllabi_library_dev
      - SECRET_KEY_BASE=your-secret-key-base-here-replace-in-production
      - PHX_HOST=localhost
      - PHX_PORT=4000
      - COUCHDB_HOST=http://couchdb:5984
      - COUCHDB_USER=admin
      - COUCHDB_PASSWORD=admin_password
    command: >
      sh -c "
        mix deps.get &&
        cd assets && npm install && cd .. &&
        mix ecto.create &&
        mix ecto.migrate &&
        mix phx.server
      "
    stdin_open: true
    tty: true

  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    environment:
      POSTGRES_DB: syllabi_library_dev
      POSTGRES_USER: syllabi_user
      POSTGRES_PASSWORD: syllabi_pass
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./priv/repo/init.sql:/docker-entrypoint-initdb.d/init.sql

  # CouchDB Database
  couchdb:
    image: couchdb:3.3.3
    environment:
      COUCHDB_USER: admin
      COUCHDB_PASSWORD: admin_password
      COUCHDB_SECRET: your-couchdb-secret-here
    ports:
      - "5984:5984"
    volumes:
      - couchdb_data:/opt/couchdb/data
      - ./couchdb/local.ini:/opt/couchdb/etc/local.d/local.ini

  # Test PostgreSQL for testing
  postgres_test:
    image: postgres:16-alpine
    environment:
      POSTGRES_DB: syllabi_library_test
      POSTGRES_USER: syllabi_user
      POSTGRES_PASSWORD: syllabi_pass
    ports:
      - "5433:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
    profiles:
      - test

volumes:
  postgres_data:
  postgres_test_data:
  couchdb_data: