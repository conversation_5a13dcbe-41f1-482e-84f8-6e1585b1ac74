{<<"links">>,
 [{<<"GitHub">>,<<"https://github.com/elixir-makeup/makeup_erlang">>}]}.
{<<"name">>,<<"makeup_erlang">>}.
{<<"version">>,<<"1.0.2">>}.
{<<"description">>,<<"Erlang lexer for the Makeup syntax highlighter.">>}.
{<<"elixir">>,<<"~> 1.6">>}.
{<<"files">>,
 [<<"lib">>,<<"lib/makeup">>,<<"lib/makeup/lexers">>,
  <<"lib/makeup/lexers/erlang_lexer.ex">>,
  <<"lib/makeup/lexers/erlang_lexer">>,
  <<"lib/makeup/lexers/erlang_lexer/testing.ex">>,
  <<"lib/makeup/lexers/erlang_lexer/application.ex">>,
  <<"lib/makeup/lexers/erlang_lexer/helper.ex">>,<<".formatter.exs">>,
  <<"mix.exs">>,<<"README.md">>]}.
{<<"app">>,<<"makeup_erlang">>}.
{<<"licenses">>,[<<"BSD-2-Clause">>]}.
{<<"requirements">>,
 [[{<<"name">>,<<"makeup">>},
   {<<"app">>,<<"makeup">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 1.0">>},
   {<<"repository">>,<<"hexpm">>}]]}.
{<<"build_tools">>,[<<"mix">>]}.
