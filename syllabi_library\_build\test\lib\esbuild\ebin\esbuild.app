{application,esbuild,
             [{modules,['Elixir.Esbuild','Elixir.Esbuild.NpmRegistry',
                        'Elixir.Mix.Tasks.Esbuild',
                        'Elixir.Mix.Tasks.Esbuild.Install']},
              {optional_applications,[inets,ssl]},
              {applications,[kernel,stdlib,elixir,logger,inets,ssl,jason]},
              {description,"Mix tasks for installing and invoking esbuild"},
              {registered,[]},
              {vsn,"0.10.0"},
              {mod,{'Elixir.Esbuild',[]}},
              {env,[{default,[]}]}]}.
