{application,syllabi_library,
    [{modules,
         ['Elixir.Inspect.SyllabiLibrary.Accounts.User',
          'Elixir.SyllabiLibrary','Elixir.SyllabiLibrary.Accounts',
          'Elixir.SyllabiLibrary.Accounts.Scope',
          'Elixir.SyllabiLibrary.Accounts.Tenant',
          'Elixir.SyllabiLibrary.Accounts.User',
          'Elixir.SyllabiLibrary.Accounts.UserNotifier',
          'Elixir.SyllabiLibrary.Accounts.UserTenant',
          'Elixir.SyllabiLibrary.Accounts.UserToken',
          'Elixir.SyllabiLibrary.AccountsFixtures',
          'Elixir.SyllabiLibrary.Application',
          'Elixir.SyllabiLibrary.DataCase','Elixir.SyllabiLibrary.Mailer',
          'Elixir.SyllabiLibrary.Repo','Elixir.SyllabiLibraryWeb',
          'Elixir.SyllabiLibraryWeb.ConnCase',
          'Elixir.SyllabiLibraryWeb.CoreComponents',
          'Elixir.SyllabiLibraryWeb.Endpoint',
          'Elixir.SyllabiLibraryWeb.ErrorHTML',
          'Elixir.SyllabiLibraryWeb.ErrorJSON',
          'Elixir.SyllabiLibraryWeb.Gettext',
          'Elixir.SyllabiLibraryWeb.Layouts',
          'Elixir.SyllabiLibraryWeb.PageController',
          'Elixir.SyllabiLibraryWeb.PageHTML',
          'Elixir.SyllabiLibraryWeb.Router',
          'Elixir.SyllabiLibraryWeb.Telemetry',
          'Elixir.SyllabiLibraryWeb.UserAuth',
          'Elixir.SyllabiLibraryWeb.UserLive.Confirmation',
          'Elixir.SyllabiLibraryWeb.UserLive.Login',
          'Elixir.SyllabiLibraryWeb.UserLive.Registration',
          'Elixir.SyllabiLibraryWeb.UserLive.Settings',
          'Elixir.SyllabiLibraryWeb.UserSessionController']},
     {compile_env,
         [{syllabi_library,
              ['Elixir.SyllabiLibraryWeb.Endpoint',code_reloader],
              error},
          {syllabi_library,
              ['Elixir.SyllabiLibraryWeb.Endpoint',debug_errors],
              error},
          {syllabi_library,
              ['Elixir.SyllabiLibraryWeb.Endpoint',force_ssl],
              error},
          {syllabi_library,['Elixir.SyllabiLibraryWeb.Gettext'],error},
          {syllabi_library,[dev_routes],error}]},
     {optional_applications,[]},
     {applications,
         [kernel,stdlib,elixir,logger,runtime_tools,pbkdf2_elixir,phoenix,
          phoenix_ecto,ecto_sql,postgrex,phoenix_html,phoenix_live_view,
          lazy_html,phoenix_live_dashboard,swoosh,req,telemetry_metrics,
          telemetry_poller,gettext,jason,dns_cluster,bandit]},
     {description,"syllabi_library"},
     {registered,[]},
     {vsn,"0.1.0"},
     {mod,{'Elixir.SyllabiLibrary.Application',[]}}]}.
