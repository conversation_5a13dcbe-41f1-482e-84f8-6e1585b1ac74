defmodule SyllabiLibrary.Accounts.UserTenantTest do
  use SyllabiLibrary.DataCase

  alias SyllabiLibrary.Accounts.{UserTenant, User, Tenant}

  describe "request_changeset/2" do
    setup do
      user_id = Ecto.UUID.generate()
      tenant_id = Ecto.UUID.generate()

      %{
        valid_attrs: %{user_id: user_id, tenant_id: tenant_id, role: "member"},
        user_id: user_id,
        tenant_id: tenant_id
      }
    end

    test "changeset with valid attributes", %{valid_attrs: attrs} do
      changeset = UserTenant.request_changeset(%UserTenant{}, attrs)
      assert changeset.valid?
      assert get_field(changeset, :status) == "pending"
      assert get_field(changeset, :role) == "member"
      assert get_field(changeset, :requested_at) != nil
    end

    test "changeset requires user_id and tenant_id", %{valid_attrs: attrs} do
      changeset = UserTenant.request_changeset(%UserTenant{}, Map.delete(attrs, :user_id))
      refute changeset.valid?
      assert %{user_id: ["can't be blank"]} = errors_on(changeset)

      changeset = UserTenant.request_changeset(%UserTenant{}, Map.delete(attrs, :tenant_id))
      refute changeset.valid?
      assert %{tenant_id: ["can't be blank"]} = errors_on(changeset)
    end

    test "changeset validates role inclusion", %{valid_attrs: attrs} do
      invalid_attrs = %{attrs | role: "invalid_role"}
      changeset = UserTenant.request_changeset(%UserTenant{}, invalid_attrs)
      refute changeset.valid?
      assert %{role: ["is invalid"]} = errors_on(changeset)
    end

    test "changeset allows valid roles", %{valid_attrs: attrs} do
      for role <- UserTenant.valid_roles() do
        role_attrs = %{attrs | role: role}
        changeset = UserTenant.request_changeset(%UserTenant{}, role_attrs)
        assert changeset.valid?, "Role #{role} should be valid"
        assert get_field(changeset, :role) == role
      end
    end
  end

  describe "auto_approve_changeset/2" do
    test "creates approved association" do
      user_id = Ecto.UUID.generate()
      tenant_id = Ecto.UUID.generate()
      attrs = %{user_id: user_id, tenant_id: tenant_id, role: "admin"}

      changeset = UserTenant.auto_approve_changeset(%UserTenant{}, attrs)
      assert changeset.valid?
      assert get_field(changeset, :status) == "approved"
      assert get_field(changeset, :requested_at) != nil
      assert get_field(changeset, :approved_at) != nil
    end
  end

  describe "approve_changeset/2" do
    test "approves a pending association" do
      approver_id = Ecto.UUID.generate()
      user_tenant = %UserTenant{status: "pending"}

      changeset = UserTenant.approve_changeset(user_tenant, %{approved_by: approver_id})
      assert changeset.valid?
      assert get_field(changeset, :status) == "approved"
      assert get_field(changeset, :approved_by) == approver_id
      assert get_field(changeset, :approved_at) != nil
    end

    test "requires approved_by" do
      user_tenant = %UserTenant{status: "pending"}
      changeset = UserTenant.approve_changeset(user_tenant, %{})
      refute changeset.valid?
      assert %{approved_by: ["can't be blank"]} = errors_on(changeset)
    end

    test "validates pending status" do
      user_tenant = %UserTenant{status: "approved"}
      approver_id = Ecto.UUID.generate()

      changeset = UserTenant.approve_changeset(user_tenant, %{approved_by: approver_id})
      refute changeset.valid?
      assert %{status: ["must be pending, current status: approved"]} = errors_on(changeset)
    end
  end

  describe "reject_changeset/2" do
    test "rejects a pending association" do
      approver_id = Ecto.UUID.generate()
      user_tenant = %UserTenant{status: "pending"}

      attrs = %{approved_by: approver_id, rejection_reason: "Not authorized"}
      changeset = UserTenant.reject_changeset(user_tenant, attrs)

      assert changeset.valid?
      assert get_field(changeset, :status) == "rejected"
      assert get_field(changeset, :approved_by) == approver_id
      assert get_field(changeset, :rejection_reason) == "Not authorized"
      assert get_field(changeset, :approved_at) != nil
    end

    test "requires approved_by and rejection_reason" do
      user_tenant = %UserTenant{status: "pending"}
      changeset = UserTenant.reject_changeset(user_tenant, %{})
      refute changeset.valid?

      errors = errors_on(changeset)
      assert %{approved_by: ["can't be blank"]} = errors
      assert %{rejection_reason: ["can't be blank"]} = errors
    end
  end

  describe "valid_roles/0" do
    test "returns expected roles" do
      assert "admin" in UserTenant.valid_roles()
      assert "member" in UserTenant.valid_roles()
      assert length(UserTenant.valid_roles()) == 2
    end
  end

  describe "valid_statuses/0" do
    test "returns expected statuses" do
      expected_statuses = ["pending", "approved", "rejected", "revoked"]
      assert UserTenant.valid_statuses() == expected_statuses
    end
  end
end