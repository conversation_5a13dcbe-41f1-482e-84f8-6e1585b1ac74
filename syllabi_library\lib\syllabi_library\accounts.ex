defmodule SyllabiLibrary.Accounts do
  @moduledoc """
  The Accounts context.
  """

  import Ecto.Query, warn: false
  alias SyllabiLibrary.Repo

  alias SyllabiLibrary.Accounts.{User, UserToken, UserNotifier, Tenant, UserTenant}

  ## Database getters

  @doc """
  Gets a user by email.

  ## Examples

      iex> get_user_by_email("<EMAIL>")
      %User{}

      iex> get_user_by_email("<EMAIL>")
      nil

  """
  def get_user_by_email(email) when is_binary(email) do
    Repo.get_by(User, email: email)
  end

  @doc """
  Gets a user by email and password.

  ## Examples

      iex> get_user_by_email_and_password("<EMAIL>", "correct_password")
      %User{}

      iex> get_user_by_email_and_password("<EMAIL>", "invalid_password")
      nil

  """
  def get_user_by_email_and_password(email, password)
      when is_binary(email) and is_binary(password) do
    user = Repo.get_by(User, email: email)
    if User.valid_password?(user, password), do: user
  end

  @doc """
  Gets a single user.

  Raises `Ecto.NoResultsError` if the User does not exist.

  ## Examples

      iex> get_user!(123)
      %User{}

      iex> get_user!(456)
      ** (Ecto.NoResultsError)

  """
  def get_user!(id), do: Repo.get!(User, id)

  ## User registration

  @doc """
  Registers a user.

  ## Examples

      iex> register_user(%{field: value})
      {:ok, %User{}}

      iex> register_user(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def register_user(attrs) do
    %User{}
    |> User.email_changeset(attrs)
    |> Repo.insert()
  end

  ## Settings

  @doc """
  Checks whether the user is in sudo mode.

  The user is in sudo mode when the last authentication was done no further
  than 20 minutes ago. The limit can be given as second argument in minutes.
  """
  def sudo_mode?(user, minutes \\ -20)

  def sudo_mode?(%User{authenticated_at: ts}, minutes) when is_struct(ts, DateTime) do
    DateTime.after?(ts, DateTime.utc_now() |> DateTime.add(minutes, :minute))
  end

  def sudo_mode?(_user, _minutes), do: false

  @doc """
  Returns an `%Ecto.Changeset{}` for changing the user email.

  See `SyllabiLibrary.Accounts.User.email_changeset/3` for a list of supported options.

  ## Examples

      iex> change_user_email(user)
      %Ecto.Changeset{data: %User{}}

  """
  def change_user_email(user, attrs \\ %{}, opts \\ []) do
    User.email_changeset(user, attrs, opts)
  end

  @doc """
  Updates the user email using the given token.

  If the token matches, the user email is updated and the token is deleted.
  """
  def update_user_email(user, token) do
    context = "change:#{user.email}"

    Repo.transact(fn ->
      with {:ok, query} <- UserToken.verify_change_email_token_query(token, context),
           %UserToken{sent_to: email} <- Repo.one(query),
           {:ok, user} <- Repo.update(User.email_changeset(user, %{email: email})),
           {_count, _result} <-
             Repo.delete_all(from(UserToken, where: [user_id: ^user.id, context: ^context])) do
        {:ok, user}
      else
        _ -> {:error, :transaction_aborted}
      end
    end)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for changing the user password.

  See `SyllabiLibrary.Accounts.User.password_changeset/3` for a list of supported options.

  ## Examples

      iex> change_user_password(user)
      %Ecto.Changeset{data: %User{}}

  """
  def change_user_password(user, attrs \\ %{}, opts \\ []) do
    User.password_changeset(user, attrs, opts)
  end

  @doc """
  Updates the user password.

  Returns a tuple with the updated user, as well as a list of expired tokens.

  ## Examples

      iex> update_user_password(user, %{password: ...})
      {:ok, {%User{}, [...]}}

      iex> update_user_password(user, %{password: "too short"})
      {:error, %Ecto.Changeset{}}

  """
  def update_user_password(user, attrs) do
    user
    |> User.password_changeset(attrs)
    |> update_user_and_delete_all_tokens()
  end

  ## Session

  @doc """
  Generates a session token.
  """
  def generate_user_session_token(user) do
    {token, user_token} = UserToken.build_session_token(user)
    Repo.insert!(user_token)
    token
  end

  @doc """
  Gets the user with the given signed token.

  If the token is valid `{user, token_inserted_at}` is returned, otherwise `nil` is returned.
  """
  def get_user_by_session_token(token) do
    {:ok, query} = UserToken.verify_session_token_query(token)
    Repo.one(query)
  end

  @doc """
  Gets the user with the given magic link token.
  """
  def get_user_by_magic_link_token(token) do
    with {:ok, query} <- UserToken.verify_magic_link_token_query(token),
         {user, _token} <- Repo.one(query) do
      user
    else
      _ -> nil
    end
  end

  @doc """
  Logs the user in by magic link.

  There are three cases to consider:

  1. The user has already confirmed their email. They are logged in
     and the magic link is expired.

  2. The user has not confirmed their email and no password is set.
     In this case, the user gets confirmed, logged in, and all tokens -
     including session ones - are expired. In theory, no other tokens
     exist but we delete all of them for best security practices.

  3. The user has not confirmed their email but a password is set.
     This cannot happen in the default implementation but may be the
     source of security pitfalls. See the "Mixing magic link and password registration" section of
     `mix help phx.gen.auth`.
  """
  def login_user_by_magic_link(token) do
    {:ok, query} = UserToken.verify_magic_link_token_query(token)

    case Repo.one(query) do
      # Prevent session fixation attacks by disallowing magic links for unconfirmed users with password
      {%User{confirmed_at: nil, hashed_password: hash}, _token} when not is_nil(hash) ->
        raise """
        magic link log in is not allowed for unconfirmed users with a password set!

        This cannot happen with the default implementation, which indicates that you
        might have adapted the code to a different use case. Please make sure to read the
        "Mixing magic link and password registration" section of `mix help phx.gen.auth`.
        """

      {%User{confirmed_at: nil} = user, _token} ->
        user
        |> User.confirm_changeset()
        |> update_user_and_delete_all_tokens()

      {user, token} ->
        Repo.delete!(token)
        {:ok, {user, []}}

      nil ->
        {:error, :not_found}
    end
  end

  @doc ~S"""
  Delivers the update email instructions to the given user.

  ## Examples

      iex> deliver_user_update_email_instructions(user, current_email, &url(~p"/users/settings/confirm-email/#{&1}"))
      {:ok, %{to: ..., body: ...}}

  """
  def deliver_user_update_email_instructions(%User{} = user, current_email, update_email_url_fun)
      when is_function(update_email_url_fun, 1) do
    {encoded_token, user_token} = UserToken.build_email_token(user, "change:#{current_email}")

    Repo.insert!(user_token)
    UserNotifier.deliver_update_email_instructions(user, update_email_url_fun.(encoded_token))
  end

  @doc """
  Delivers the magic link login instructions to the given user.
  """
  def deliver_login_instructions(%User{} = user, magic_link_url_fun)
      when is_function(magic_link_url_fun, 1) do
    {encoded_token, user_token} = UserToken.build_email_token(user, "login")
    Repo.insert!(user_token)
    UserNotifier.deliver_login_instructions(user, magic_link_url_fun.(encoded_token))
  end

  @doc """
  Deletes the signed token with the given context.
  """
  def delete_user_session_token(token) do
    Repo.delete_all(from(UserToken, where: [token: ^token, context: "session"]))
    :ok
  end

  ## Token helper

  defp update_user_and_delete_all_tokens(changeset) do
    Repo.transact(fn ->
      with {:ok, user} <- Repo.update(changeset) do
        tokens_to_expire = Repo.all_by(UserToken, user_id: user.id)

        Repo.delete_all(from(t in UserToken, where: t.id in ^Enum.map(tokens_to_expire, & &1.id)))

        {:ok, {user, tokens_to_expire}}
      end
    end)
  end

  ## Tenant management

  @doc """
  Returns the list of tenants.

  ## Examples

      iex> list_tenants()
      [%Tenant{}, ...]

  """
  def list_tenants do
    Repo.all(Tenant)
  end

  @doc """
  Gets a single tenant.

  Raises `Ecto.NoResultsError` if the Tenant does not exist.

  ## Examples

      iex> get_tenant!(123)
      %Tenant{}

      iex> get_tenant!(456)
      ** (Ecto.NoResultsError)

  """
  def get_tenant!(id), do: Repo.get!(Tenant, id)

  @doc """
  Gets a tenant by slug.

  ## Examples

      iex> get_tenant_by_slug("university-abc")
      %Tenant{}

      iex> get_tenant_by_slug("nonexistent")
      nil

  """
  def get_tenant_by_slug(slug) when is_binary(slug) do
    Repo.get_by(Tenant, slug: slug)
  end

  @doc """
  Gets a tenant by subdomain.

  ## Examples

      iex> get_tenant_by_subdomain("university-abc.syllabi-library.com")
      %Tenant{}

      iex> get_tenant_by_subdomain("nonexistent.syllabi-library.com")
      nil

  """
  def get_tenant_by_subdomain(subdomain) when is_binary(subdomain) do
    Repo.get_by(Tenant, subdomain: subdomain)
  end

  @doc """
  Creates a tenant and assigns the given user as the first admin.

  ## Examples

      iex> create_tenant_with_admin(user, %{name: "University ABC", slug: "university-abc"})
      {:ok, {%Tenant{}, %UserTenant{}}}

      iex> create_tenant_with_admin(user, %{name: ""})
      {:error, %Ecto.Changeset{}}

  """
  def create_tenant_with_admin(user, attrs \\ %{}) do
    Repo.transaction(fn ->
      tenant_changeset = Tenant.changeset(%Tenant{}, attrs)

      with {:ok, tenant} <- Repo.insert(tenant_changeset),
           user_tenant_attrs = %{user_id: user.id, tenant_id: tenant.id, role: "admin"},
           user_tenant_changeset = UserTenant.auto_approve_changeset(%UserTenant{}, user_tenant_attrs),
           {:ok, user_tenant} <- Repo.insert(user_tenant_changeset) do
        {tenant, user_tenant}
      else
        {:error, changeset} -> Repo.rollback(changeset)
      end
    end)
  end

  @doc """
  Updates a tenant.

  ## Examples

      iex> update_tenant(tenant, %{name: "New Name"})
      {:ok, %Tenant{}}

      iex> update_tenant(tenant, %{name: ""})
      {:error, %Ecto.Changeset{}}

  """
  def update_tenant(%Tenant{} = tenant, attrs) do
    tenant
    |> Tenant.update_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking tenant changes.

  ## Examples

      iex> change_tenant(tenant)
      %Ecto.Changeset{data: %Tenant{}}

  """
  def change_tenant(%Tenant{} = tenant, attrs \\ %{}) do
    Tenant.changeset(tenant, attrs)
  end

  ## User-Tenant associations

  @doc """
  Creates a user-tenant association request.

  ## Examples

      iex> request_tenant_access(user, tenant, "member")
      {:ok, %UserTenant{}}

      iex> request_tenant_access(user, tenant, "invalid_role")
      {:error, %Ecto.Changeset{}}

  """
  def request_tenant_access(user, tenant, role \\ "member") do
    attrs = %{user_id: user.id, tenant_id: tenant.id, role: role}

    %UserTenant{}
    |> UserTenant.request_changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Gets user-tenant associations for a user.

  ## Examples

      iex> get_user_tenants(user)
      [%UserTenant{}, ...]

  """
  def get_user_tenants(user) do
    from(ut in UserTenant,
      where: ut.user_id == ^user.id,
      preload: [:tenant]
    )
    |> Repo.all()
  end

  @doc """
  Gets user-tenant associations for a tenant.

  ## Examples

      iex> get_tenant_users(tenant)
      [%UserTenant{}, ...]

  """
  def get_tenant_users(tenant) do
    from(ut in UserTenant,
      where: ut.tenant_id == ^tenant.id,
      preload: [:user]
    )
    |> Repo.all()
  end

  @doc """
  Approves a pending user-tenant association.

  ## Examples

      iex> approve_tenant_access(user_tenant, admin_user)
      {:ok, %UserTenant{}}

      iex> approve_tenant_access(already_approved_user_tenant, admin_user)
      {:error, %Ecto.Changeset{}}

  """
  def approve_tenant_access(%UserTenant{} = user_tenant, approver) do
    user_tenant
    |> UserTenant.approve_changeset(%{approved_by: approver.id})
    |> Repo.update()
  end

  @doc """
  Rejects a pending user-tenant association.

  ## Examples

      iex> reject_tenant_access(user_tenant, admin_user, "Not authorized")
      {:ok, %UserTenant{}}

  """
  def reject_tenant_access(%UserTenant{} = user_tenant, approver, reason) do
    user_tenant
    |> UserTenant.reject_changeset(%{approved_by: approver.id, rejection_reason: reason})
    |> Repo.update()
  end

  @doc """
  Checks if a user is an admin for a tenant.

  ## Examples

      iex> user_admin_for_tenant?(user, tenant)
      true

  """
  def user_admin_for_tenant?(user, tenant) do
    from(ut in UserTenant,
      where: ut.user_id == ^user.id and ut.tenant_id == ^tenant.id and
             ut.status == "approved" and ut.role == "admin"
    )
    |> Repo.exists?()
  end
end
