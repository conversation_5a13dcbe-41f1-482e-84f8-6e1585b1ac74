defmodule SyllabiLibrary.Accounts.Tenant do
  @moduledoc """
  The Tenant schema represents organizations in the syllabi library system.

  Each tenant corresponds to an educational institution (university, college, etc.)
  and gets their own subdomain for public access. Tenants contain users who can
  upload and manage syllabi for their organization.

  ## Schema

  A tenant has the following fields:
  - `name`: The full name of the organization (e.g., "University ABC")
  - `slug`: URL-friendly version for subdomain (e.g., "university-abc")
  - `subdomain`: Full subdomain for public access (e.g., "university-abc.syllabi-library.com")
  - `contact_email`: Primary admin email for approval notifications
  - `contact_name`: Name of the primary contact person
  - `settings`: JSONB field for tenant-specific configurations
  - `status`: Current status (active, suspended, pending)

  ## Example

      iex> %Tenant{}
      |> Tenant.changeset(%{
        name: "University ABC",
        slug: "university-abc",
        contact_email: "<EMAIL>"
      })
      |> Repo.insert()
  """

  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  @type t :: %__MODULE__{
          id: binary(),
          name: String.t(),
          slug: String.t(),
          subdomain: String.t(),
          contact_email: String.t() | nil,
          contact_name: String.t() | nil,
          settings: map(),
          status: String.t(),
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  schema "tenants" do
    field :name, :string
    field :slug, :string
    field :subdomain, :string
    field :contact_email, :string
    field :contact_name, :string
    field :settings, :map, default: %{}
    field :status, :string, default: "active"

    has_many :user_tenants, SyllabiLibrary.Accounts.UserTenant
    has_many :users, through: [:user_tenants, :user]

    timestamps(type: :utc_datetime)
  end

  @doc """
  Changeset for creating a new tenant organization.

  Validates required fields and generates subdomain from slug.
  """
  @spec changeset(t(), map()) :: Ecto.Changeset.t()
  def changeset(tenant, attrs) do
    tenant
    |> cast(attrs, [:name, :slug, :contact_email, :contact_name, :settings, :status])
    |> validate_required([:name, :slug])
    |> validate_format(:slug, ~r/^[a-z0-9-]+$/, message: "must contain only lowercase letters, numbers, and hyphens")
    |> validate_length(:slug, min: 3, max: 50)
    |> validate_format(:contact_email, ~r/@/, message: "must be a valid email")
    |> unique_constraint(:slug)
    |> unique_constraint(:subdomain)
    |> put_subdomain()
  end

  @doc """
  Changeset for updating tenant settings.
  """
  @spec update_changeset(t(), map()) :: Ecto.Changeset.t()
  def update_changeset(tenant, attrs) do
    tenant
    |> cast(attrs, [:name, :contact_email, :contact_name, :settings, :status])
    |> validate_required([:name])
    |> validate_format(:contact_email, ~r/@/, message: "must be a valid email")
  end

  # Private function to generate subdomain from slug
  defp put_subdomain(changeset) do
    case get_field(changeset, :slug) do
      nil -> changeset
      slug -> put_change(changeset, :subdomain, "#{slug}.syllabi-library.com")
    end
  end
end