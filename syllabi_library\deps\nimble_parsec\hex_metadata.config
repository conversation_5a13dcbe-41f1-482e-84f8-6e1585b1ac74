{<<"links">>,
 [{<<"GitHub">>,<<"https://github.com/dashbitco/nimble_parsec">>}]}.
{<<"name">>,<<"nimble_parsec">>}.
{<<"version">>,<<"1.4.2">>}.
{<<"description">>,
 <<"A simple and fast library for text-based parser combinators">>}.
{<<"elixir">>,<<"~> 1.12">>}.
{<<"files">>,
 [<<"lib">>,<<"lib/nimble_parsec">>,<<"lib/nimble_parsec/recorder.ex">>,
  <<"lib/nimble_parsec/compiler.ex">>,<<"lib/mix">>,<<"lib/mix/tasks">>,
  <<"lib/mix/tasks/nimble_parsec.compile.ex">>,<<"lib/nimble_parsec.ex">>,
  <<".formatter.exs">>,<<"mix.exs">>,<<"README.md">>,<<"CHANGELOG.md">>]}.
{<<"app">>,<<"nimble_parsec">>}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"requirements">>,[]}.
{<<"build_tools">>,[<<"mix">>]}.
