{<<"links">>,
 [{<<"Changelog">>,<<"https://hexdocs.pm/makeup/changelog.html">>},
  {<<"Contributing">>,<<"https://hexdocs.pm/makeup/contributing.html">>},
  {<<"GitHub">>,<<"https://github.com/elixir-makeup/makeup">>}]}.
{<<"name">>,<<"makeup">>}.
{<<"version">>,<<"1.2.1">>}.
{<<"description">>,
 <<"Syntax highlighter for source code in the style of Pygments.">>}.
{<<"elixir">>,<<"~> 1.12">>}.
{<<"app">>,<<"makeup">>}.
{<<"licenses">>,[<<"BSD-2-Clause">>]}.
{<<"requirements">>,
 [[{<<"name">>,<<"nimble_parsec">>},
   {<<"app">>,<<"nimble_parsec">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 1.4">>},
   {<<"repository">>,<<"hexpm">>}]]}.
{<<"files">>,
 [<<"lib">>,<<"lib/makeup">>,<<"lib/makeup/token">>,
  <<"lib/makeup/token/utils">>,<<"lib/makeup/token/utils/hierarchy.ex">>,
  <<"lib/makeup/token/utils.ex">>,<<"lib/makeup/registry.ex">>,
  <<"lib/makeup/lexer">>,<<"lib/makeup/lexer/postprocess.ex">>,
  <<"lib/makeup/lexer/types.ex">>,<<"lib/makeup/lexer/groups.ex">>,
  <<"lib/makeup/lexer/combinators.ex">>,<<"lib/makeup/formatter.ex">>,
  <<"lib/makeup/formatters">>,<<"lib/makeup/formatters/html">>,
  <<"lib/makeup/formatters/html/html_formatter.ex">>,
  <<"lib/makeup/formatters/html/scripts">>,
  <<"lib/makeup/formatters/html/scripts/group_highlighter_javascript.js">>,
  <<"lib/makeup/styles">>,<<"lib/makeup/styles/html">>,
  <<"lib/makeup/styles/html/token_style.ex">>,
  <<"lib/makeup/styles/html/style_map.ex">>,
  <<"lib/makeup/styles/html/style.ex">>,<<"lib/makeup/application.ex">>,
  <<"lib/makeup/lexer.ex">>,<<"lib/makeup.ex">>,<<"mix.exs">>,<<"README.md">>,
  <<"LICENSE">>,<<"CHANGELOG.md">>]}.
{<<"build_tools">>,[<<"mix">>]}.
