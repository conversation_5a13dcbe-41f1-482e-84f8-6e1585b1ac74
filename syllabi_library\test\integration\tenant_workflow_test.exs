defmodule SyllabiLibrary.Integration.TenantWorkflowTest do
  @moduledoc """
  Integration test demonstrating the complete tenant workflow:
  1. Create a tenant with admin user
  2. Add another user to the tenant with approval workflow
  3. Verify the test files structure exists for bulk upload
  """
  use SyllabiLibrary.DataCase

  alias SyllabiLibrary.Accounts

  import SyllabiLibrary.AccountsFixtures

  describe "complete tenant workflow" do
    test "create tenant, add user, and prepare for file upload" do
      # Step 1: Create a tenant with admin user (simulating first user registration)
      admin_user = user_fixture(email: "<EMAIL>")
      tenant_attrs = %{
        name: "University ABC",
        slug: "university-abc",
        contact_email: "<EMAIL>"
      }

      assert {:ok, {tenant, admin_user_tenant}} =
        Accounts.create_tenant_with_admin(admin_user, tenant_attrs)

      assert tenant.name == "University ABC"
      assert tenant.slug == "university-abc"
      assert tenant.subdomain == "university-abc.syllabi-library.com"
      assert admin_user_tenant.role == "admin"
      assert admin_user_tenant.status == "approved"

      # Step 2: Another user requests to join the tenant
      member_user = user_fixture(email: "<EMAIL>")

      assert {:ok, member_user_tenant} =
        Accounts.request_tenant_access(member_user, tenant, "member")

      assert member_user_tenant.status == "pending"

      # Step 3: Admin approves the request
      assert {:ok, approved_user_tenant} =
        Accounts.approve_tenant_access(member_user_tenant, admin_user)

      assert approved_user_tenant.status == "approved"
      assert approved_user_tenant.approved_by == admin_user.id

      # Step 4: Verify both users are associated with the tenant
      tenant_users = Accounts.get_tenant_users(tenant)
      user_emails = Enum.map(tenant_users, & &1.user.email)

      assert "<EMAIL>" in user_emails
      assert "<EMAIL>" in user_emails

      # Step 5: Verify admin privileges
      assert Accounts.user_admin_for_tenant?(admin_user, tenant) == true
      assert Accounts.user_admin_for_tenant?(member_user, tenant) == false

      # Step 6: Verify test files structure exists for bulk upload
      test_files_dir = Path.join([File.cwd!(), "test_files"])
      assert File.exists?(test_files_dir)

      expected_files = [
        "cs201-data-structures.txt",
        "webd201-web-development.txt",
        "nurs110-fundamentals.txt"
      ]

      for file <- expected_files do
        file_path = Path.join(test_files_dir, file)
        assert File.exists?(file_path), "Test file #{file} should exist"

        # Verify file has content
        content = File.read!(file_path)
        assert String.length(content) > 100, "Test file should have substantial content"
        assert String.contains?(content, "Course:"), "Should contain course information"
        assert String.contains?(content, "Organization:"), "Should contain organization info"
      end
    end

    test "demonstrate tenant isolation" do
      # Create two different tenants
      admin1 = user_fixture(email: "<EMAIL>")
      admin2 = user_fixture(email: "<EMAIL>")

      {:ok, {tenant1, _}} = Accounts.create_tenant_with_admin(admin1, %{
        name: "University ABC",
        slug: "university-abc"
      })

      {:ok, {tenant2, _}} = Accounts.create_tenant_with_admin(admin2, %{
        name: "College XYZ",
        slug: "college-xyz"
      })

      # Verify tenants are isolated
      assert tenant1.id != tenant2.id
      assert tenant1.slug != tenant2.slug
      assert tenant1.subdomain != tenant2.subdomain

      # Admin1 should not be admin of tenant2
      assert Accounts.user_admin_for_tenant?(admin1, tenant2) == false
      assert Accounts.user_admin_for_tenant?(admin2, tenant1) == false
    end
  end
end