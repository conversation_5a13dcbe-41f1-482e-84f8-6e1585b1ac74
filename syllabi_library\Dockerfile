# Dockerfile for Syllabi Library Phoenix Application
FROM elixir:1.18-alpine

# Install system dependencies
RUN apk add --no-cache \
    build-base \
    git \
    curl \
    nodejs \
    npm \
    inotify-tools \
    postgresql-client

# Install hex and rebar
RUN mix local.hex --force && \
    mix local.rebar --force

# Set working directory
WORKDIR /app

# Copy dependency files
COPY mix.exs mix.lock ./

# Install Phoenix and dependencies
RUN mix archive.install --force hex phx_new && \
    mix deps.get

# Copy assets (if they exist)
COPY assets assets/

# Set environment variables
ENV MIX_ENV=dev
ENV PHX_HOST=0.0.0.0
ENV PHX_PORT=4000

# Create directories for file uploads and processing
RUN mkdir -p /app/uploads/temp && \
    mkdir -p /app/uploads/processing && \
    mkdir -p /app/uploads/storage && \
    mkdir -p /app/test_files

# Expose port
EXPOSE 4000

# Default command
CMD ["mix", "phx.server"]