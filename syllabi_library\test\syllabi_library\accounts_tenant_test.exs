defmodule SyllabiLibrary.AccountsTenantTest do
  use SyllabiLibrary.DataCase

  alias SyllabiLibrary.Accounts
  alias SyllabiLibrary.Accounts.{Tenant, UserTenant}

  import SyllabiLibrary.AccountsFixtures

  describe "tenants" do
    @valid_attrs %{
      name: "University ABC",
      slug: "university-abc",
      contact_email: "<EMAIL>"
    }
    @update_attrs %{
      name: "University XYZ",
      contact_email: "<EMAIL>"
    }
    @invalid_attrs %{name: nil, slug: nil}

    def tenant_fixture(attrs \\ %{}) do
      {:ok, tenant} =
        attrs
        |> Enum.into(@valid_attrs)
        |> (&Accounts.create_tenant_with_admin(user_fixture(), &1)).()

      elem(tenant, 0)  # Return just the tenant from the tuple
    end

    test "list_tenants/0 returns all tenants" do
      tenant = tenant_fixture()
      assert Accounts.list_tenants() == [tenant]
    end

    test "get_tenant!/1 returns the tenant with given id" do
      tenant = tenant_fixture()
      assert Accounts.get_tenant!(tenant.id) == tenant
    end

    test "get_tenant_by_slug/1 returns tenant with given slug" do
      tenant = tenant_fixture(%{slug: "test-university"})
      assert Accounts.get_tenant_by_slug("test-university") == tenant
      assert Accounts.get_tenant_by_slug("nonexistent") == nil
    end

    test "get_tenant_by_subdomain/1 returns tenant with given subdomain" do
      tenant = tenant_fixture(%{slug: "test-university"})
      subdomain = "test-university.syllabi-library.com"

      assert Accounts.get_tenant_by_subdomain(subdomain) == tenant
      assert Accounts.get_tenant_by_subdomain("nonexistent.syllabi-library.com") == nil
    end

    test "create_tenant_with_admin/2 with valid data creates a tenant and admin user" do
      user = user_fixture()
      valid_attrs = %{name: "Test University", slug: "test-university"}

      assert {:ok, {tenant, user_tenant}} = Accounts.create_tenant_with_admin(user, valid_attrs)
      assert tenant.name == "Test University"
      assert tenant.slug == "test-university"
      assert tenant.subdomain == "test-university.syllabi-library.com"

      assert user_tenant.user_id == user.id
      assert user_tenant.tenant_id == tenant.id
      assert user_tenant.role == "admin"
      assert user_tenant.status == "approved"
    end

    test "create_tenant_with_admin/2 with invalid data returns error changeset" do
      user = user_fixture()
      assert {:error, changeset} = Accounts.create_tenant_with_admin(user, @invalid_attrs)
      assert %{name: ["can't be blank"], slug: ["can't be blank"]} = errors_on(changeset)
    end

    test "create_tenant_with_admin/2 rolls back on user_tenant creation failure" do
      # This test would require mocking to simulate the failure scenario
      # For now, we'll test that duplicate slugs are handled correctly
      user1 = user_fixture()
      user2 = user_fixture()

      {:ok, _} = Accounts.create_tenant_with_admin(user1, %{name: "First", slug: "duplicate"})

      assert {:error, changeset} = Accounts.create_tenant_with_admin(user2, %{name: "Second", slug: "duplicate"})
      assert %{slug: ["has already been taken"]} = errors_on(changeset)
    end

    test "update_tenant/2 with valid data updates the tenant" do
      tenant = tenant_fixture()
      assert {:ok, %Tenant{} = tenant} = Accounts.update_tenant(tenant, @update_attrs)
      assert tenant.name == "University XYZ"
      assert tenant.contact_email == "<EMAIL>"
    end

    test "update_tenant/2 with invalid data returns error changeset" do
      tenant = tenant_fixture()
      assert {:error, %Ecto.Changeset{}} = Accounts.update_tenant(tenant, @invalid_attrs)
      assert tenant == Accounts.get_tenant!(tenant.id)
    end

    test "change_tenant/1 returns a tenant changeset" do
      tenant = tenant_fixture()
      assert %Ecto.Changeset{} = Accounts.change_tenant(tenant)
    end
  end

  describe "user_tenants" do
    setup do
      user = user_fixture()
      tenant = tenant_fixture()
      %{user: user, tenant: tenant}
    end

    test "request_tenant_access/3 creates a pending user-tenant association", %{user: user, tenant: tenant} do
      assert {:ok, %UserTenant{} = user_tenant} = Accounts.request_tenant_access(user, tenant, "member")
      assert user_tenant.user_id == user.id
      assert user_tenant.tenant_id == tenant.id
      assert user_tenant.role == "member"
      assert user_tenant.status == "pending"
    end

    test "request_tenant_access/3 defaults to member role", %{user: user, tenant: tenant} do
      assert {:ok, %UserTenant{} = user_tenant} = Accounts.request_tenant_access(user, tenant)
      assert user_tenant.role == "member"
    end

    test "request_tenant_access/3 prevents duplicate associations", %{user: user, tenant: tenant} do
      assert {:ok, _} = Accounts.request_tenant_access(user, tenant)
      assert {:error, changeset} = Accounts.request_tenant_access(user, tenant)
      assert %{user_id: ["has already been taken"]} = errors_on(changeset)
    end

    test "get_user_tenants/1 returns user's tenant associations", %{user: user, tenant: tenant} do
      {:ok, user_tenant} = Accounts.request_tenant_access(user, tenant)
      user_tenants = Accounts.get_user_tenants(user)

      assert length(user_tenants) == 1
      assert hd(user_tenants).id == user_tenant.id
      assert hd(user_tenants).tenant.id == tenant.id
    end

    test "get_tenant_users/1 returns tenant's user associations", %{user: user, tenant: tenant} do
      {:ok, user_tenant} = Accounts.request_tenant_access(user, tenant)
      tenant_users = Accounts.get_tenant_users(tenant)

      assert length(tenant_users) >= 1  # At least one from the tenant_fixture
      user_tenant_ids = Enum.map(tenant_users, & &1.id)
      assert user_tenant.id in user_tenant_ids
    end

    test "approve_tenant_access/2 approves a pending association", %{user: user, tenant: tenant} do
      approver = user_fixture()
      {:ok, user_tenant} = Accounts.request_tenant_access(user, tenant)

      assert {:ok, %UserTenant{} = approved_user_tenant} =
        Accounts.approve_tenant_access(user_tenant, approver)

      assert approved_user_tenant.status == "approved"
      assert approved_user_tenant.approved_by == approver.id
      assert approved_user_tenant.approved_at != nil
    end

    test "approve_tenant_access/2 fails for non-pending associations", %{user: user, tenant: tenant} do
      approver = user_fixture()
      {:ok, user_tenant} = Accounts.request_tenant_access(user, tenant)
      {:ok, approved_user_tenant} = Accounts.approve_tenant_access(user_tenant, approver)

      # Try to approve again
      assert {:error, changeset} = Accounts.approve_tenant_access(approved_user_tenant, approver)
      assert %{status: ["must be pending, current status: approved"]} = errors_on(changeset)
    end

    test "reject_tenant_access/3 rejects a pending association", %{user: user, tenant: tenant} do
      approver = user_fixture()
      {:ok, user_tenant} = Accounts.request_tenant_access(user, tenant)

      assert {:ok, %UserTenant{} = rejected_user_tenant} =
        Accounts.reject_tenant_access(user_tenant, approver, "Not authorized")

      assert rejected_user_tenant.status == "rejected"
      assert rejected_user_tenant.approved_by == approver.id
      assert rejected_user_tenant.rejection_reason == "Not authorized"
      assert rejected_user_tenant.approved_at != nil
    end

    test "user_admin_for_tenant?/2 returns true for admin users", %{tenant: tenant} do
      # The tenant_fixture creates an admin user
      tenant_users = Accounts.get_tenant_users(tenant)
      admin_user_tenant = Enum.find(tenant_users, &(&1.role == "admin" && &1.status == "approved"))

      assert admin_user_tenant != nil
      admin_user = admin_user_tenant.user
      assert Accounts.user_admin_for_tenant?(admin_user, tenant) == true
    end

    test "user_admin_for_tenant?/2 returns false for non-admin users", %{user: user, tenant: tenant} do
      {:ok, user_tenant} = Accounts.request_tenant_access(user, tenant, "member")
      approver = user_fixture()
      {:ok, _} = Accounts.approve_tenant_access(user_tenant, approver)

      assert Accounts.user_admin_for_tenant?(user, tenant) == false
    end

    test "user_admin_for_tenant?/2 returns false for pending admin users", %{user: user, tenant: tenant} do
      {:ok, _user_tenant} = Accounts.request_tenant_access(user, tenant, "admin")

      assert Accounts.user_admin_for_tenant?(user, tenant) == false
    end
  end
end