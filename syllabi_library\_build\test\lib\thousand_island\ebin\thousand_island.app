{application,thousand_island,
             [{modules,['Elixir.ThousandIsland',
                        'Elixir.ThousandIsland.Acceptor',
                        'Elixir.ThousandIsland.AcceptorPoolSupervisor',
                        'Elixir.ThousandIsland.AcceptorSupervisor',
                        'Elixir.ThousandIsland.Connection',
                        'Elixir.ThousandIsland.Handler',
                        'Elixir.ThousandIsland.Listener',
                        'Elixir.ThousandIsland.Logger',
                        'Elixir.ThousandIsland.Server',
                        'Elixir.ThousandIsland.ServerConfig',
                        'Elixir.ThousandIsland.ShutdownListener',
                        'Elixir.ThousandIsland.Socket',
                        'Elixir.ThousandIsland.Telemetry',
                        'Elixir.ThousandIsland.Transport',
                        'Elixir.ThousandIsland.Transports.SSL',
                        'Elixir.ThousandIsland.Transports.TCP']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,ssl,telemetry]},
              {description,"A simple & modern pure Elixir socket server"},
              {registered,[]},
              {vsn,"1.4.1"}]}.
