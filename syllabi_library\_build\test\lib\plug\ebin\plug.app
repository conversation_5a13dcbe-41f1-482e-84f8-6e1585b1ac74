{application,plug,
             [{modules,['Elixir.Inspect.Plug.Conn','Elixir.Plug',
                        'Elixir.Plug.Adapters.Cowboy',
                        'Elixir.Plug.Adapters.Test.Conn',
                        'Elixir.Plug.Application',
                        'Elixir.Plug.BadRequestError','Elixir.Plug.BasicAuth',
                        'Elixir.Plug.Builder','Elixir.Plug.CSRFProtection',
                        'Elixir.Plug.CSRFProtection.InvalidCSRFTokenError',
                        'Elixir.Plug.CSRFProtection.InvalidCrossOriginRequestError',
                        'Elixir.Plug.Conn','Elixir.Plug.Conn.Adapter',
                        'Elixir.Plug.Conn.AlreadySentError',
                        'Elixir.Plug.Conn.CookieOverflowError',
                        'Elixir.Plug.Conn.Cookies',
                        'Elixir.Plug.Conn.InvalidHeaderError',
                        'Elixir.Plug.Conn.InvalidQueryError',
                        'Elixir.Plug.Conn.NotSentError',
                        'Elixir.Plug.Conn.Query','Elixir.Plug.Conn.Status',
                        'Elixir.Plug.Conn.Unfetched','Elixir.Plug.Conn.Utils',
                        'Elixir.Plug.Conn.WrapperError',
                        'Elixir.Plug.Debugger','Elixir.Plug.ErrorHandler',
                        'Elixir.Plug.Exception','Elixir.Plug.Exception.Any',
                        'Elixir.Plug.HTML','Elixir.Plug.Head',
                        'Elixir.Plug.Logger','Elixir.Plug.MIME',
                        'Elixir.Plug.MethodOverride','Elixir.Plug.Parsers',
                        'Elixir.Plug.Parsers.BadEncodingError',
                        'Elixir.Plug.Parsers.JSON',
                        'Elixir.Plug.Parsers.MULTIPART',
                        'Elixir.Plug.Parsers.ParseError',
                        'Elixir.Plug.Parsers.RequestTooLargeError',
                        'Elixir.Plug.Parsers.URLENCODED',
                        'Elixir.Plug.Parsers.UnsupportedMediaTypeError',
                        'Elixir.Plug.RequestId','Elixir.Plug.RewriteOn',
                        'Elixir.Plug.Router',
                        'Elixir.Plug.Router.InvalidSpecError',
                        'Elixir.Plug.Router.MalformedURIError',
                        'Elixir.Plug.Router.Utils','Elixir.Plug.SSL',
                        'Elixir.Plug.Session','Elixir.Plug.Session.COOKIE',
                        'Elixir.Plug.Session.ETS','Elixir.Plug.Session.Store',
                        'Elixir.Plug.Static',
                        'Elixir.Plug.Static.InvalidPathError',
                        'Elixir.Plug.Telemetry','Elixir.Plug.Test',
                        'Elixir.Plug.TimeoutError','Elixir.Plug.Upload',
                        'Elixir.Plug.UploadError',plug_multipart]},
              {compile_env,[{plug,[mimes],error},{plug,[statuses],error}]},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,eex,mime,plug_crypto,
                             telemetry]},
              {description,"Compose web applications with functions"},
              {registered,[]},
              {vsn,"1.18.1"},
              {mod,{'Elixir.Plug.Application',[]}},
              {env,[{validate_header_keys_during_test,true}]}]}.
