{application,syllabi_library,
    [{modules,
         ['Elixir.Inspect.SyllabiLibrary.Accounts.User',
          'Elixir.SyllabiLibrary','Elixir.SyllabiLibrary.Accounts',
          'Elixir.SyllabiLibrary.Accounts.Scope',
          'Elixir.SyllabiLibrary.Accounts.Tenant',
          'Elixir.SyllabiLibrary.Accounts.User',
          'Elixir.SyllabiLibrary.Accounts.UserNotifier',
          'Elixir.SyllabiLibrary.Accounts.UserTenant',
          'Elixir.SyllabiLibrary.Accounts.UserToken',
          'Elixir.SyllabiLibrary.Application','Elixir.SyllabiLibrary.Mailer',
          'Elixir.SyllabiLibrary.Repo','Elixir.SyllabiLibraryWeb',
          'Elixir.SyllabiLibraryWeb.CoreComponents',
          'Elixir.SyllabiLibraryWeb.Endpoint',
          'Elixir.SyllabiLibraryWeb.ErrorHTML',
          'Elixir.SyllabiLibraryWeb.ErrorJSON',
          'Elixir.SyllabiLibraryWeb.Gettext',
          'Elixir.SyllabiLibraryWeb.Layouts',
          'Elixir.SyllabiLibraryWeb.PageController',
          'Elixir.SyllabiLibraryWeb.PageHTML',
          'Elixir.SyllabiLibraryWeb.Router',
          'Elixir.SyllabiLibraryWeb.Telemetry',
          'Elixir.SyllabiLibraryWeb.UserAuth',
          'Elixir.SyllabiLibraryWeb.UserLive.Confirmation',
          'Elixir.SyllabiLibraryWeb.UserLive.Login',
          'Elixir.SyllabiLibraryWeb.UserLive.Registration',
          'Elixir.SyllabiLibraryWeb.UserLive.Settings',
          'Elixir.SyllabiLibraryWeb.UserSessionController']},
     {compile_env,
         [{syllabi_library,
              ['Elixir.SyllabiLibraryWeb.Endpoint',code_reloader],
              {ok,true}},
          {syllabi_library,
              ['Elixir.SyllabiLibraryWeb.Endpoint',debug_errors],
              {ok,true}},
          {syllabi_library,
              ['Elixir.SyllabiLibraryWeb.Endpoint',force_ssl],
              error},
          {syllabi_library,['Elixir.SyllabiLibraryWeb.Gettext'],error},
          {syllabi_library,[dev_routes],{ok,true}}]},
     {optional_applications,[]},
     {applications,
         [kernel,stdlib,elixir,logger,runtime_tools,pbkdf2_elixir,phoenix,
          phoenix_ecto,ecto_sql,postgrex,phoenix_html,phoenix_live_reload,
          phoenix_live_view,phoenix_live_dashboard,esbuild,tailwind,swoosh,
          req,telemetry_metrics,telemetry_poller,gettext,jason,dns_cluster,
          bandit]},
     {description,"syllabi_library"},
     {registered,[]},
     {vsn,"0.1.0"},
     {mod,{'Elixir.SyllabiLibrary.Application',[]}}]}.
