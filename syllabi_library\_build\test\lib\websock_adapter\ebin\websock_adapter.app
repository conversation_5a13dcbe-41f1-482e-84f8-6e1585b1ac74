{application,websock_adapter,
             [{modules,['Elixir.WebSockAdapter',
                        'Elixir.WebSockAdapter.UpgradeError',
                        'Elixir.WebSockAdapter.UpgradeValidation']},
              {optional_applications,[bandit,plug_cowboy]},
              {applications,[kernel,stdlib,elixir,websock,plug,bandit,
                             plug_cowboy]},
              {description,"A set of WebSock adapters for common web servers"},
              {registered,[]},
              {vsn,"0.5.8"}]}.
