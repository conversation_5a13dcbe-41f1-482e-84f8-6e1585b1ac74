defmodule SyllabiLibrary.Accounts.UserTenant do
  @moduledoc """
  The UserTenant schema represents the association between users and tenants,
  including approval workflow and role management.

  ## Schema

  A user-tenant association includes:
  - `user_id`: Reference to the user
  - `tenant_id`: Reference to the tenant organization
  - `role`: User's role within the organization (admin, member)
  - `status`: Approval status (pending, approved, rejected, revoked)
  - `requested_at`: When the user requested access
  - `approved_at`: When the request was approved (if applicable)
  - `approved_by`: Which admin approved the request
  - `rejection_reason`: Reason for rejection (if applicable)

  ## Example

      iex> %UserTenant{}
      |> UserTenant.request_changeset(%{
        user_id: user.id,
        tenant_id: tenant.id,
        role: "member"
      })
      |> Repo.insert()
  """

  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  @type t :: %__MODULE__{
          id: binary(),
          user_id: binary(),
          tenant_id: binary(),
          role: String.t(),
          status: String.t(),
          requested_at: DateTime.t(),
          approved_at: DateTime.t() | nil,
          approved_by: binary() | nil,
          rejection_reason: String.t() | nil,
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @valid_roles ~w(admin member)
  @valid_statuses ~w(pending approved rejected revoked)

  schema "user_tenants" do
    belongs_to :user, SyllabiLibrary.Accounts.User
    belongs_to :tenant, SyllabiLibrary.Accounts.Tenant
    belongs_to :approver, SyllabiLibrary.Accounts.User, foreign_key: :approved_by

    field :role, :string, default: "member"
    field :status, :string, default: "pending"
    field :requested_at, :utc_datetime
    field :approved_at, :utc_datetime
    field :rejection_reason, :string

    timestamps(type: :utc_datetime)
  end

  @doc """
  Changeset for creating a new user-tenant association request.
  """
  @spec request_changeset(t(), map()) :: Ecto.Changeset.t()
  def request_changeset(user_tenant, attrs) do
    user_tenant
    |> cast(attrs, [:user_id, :tenant_id, :role])
    |> validate_required([:user_id, :tenant_id])
    |> validate_inclusion(:role, @valid_roles)
    |> put_change(:status, "pending")
    |> put_change(:requested_at, DateTime.utc_now() |> DateTime.truncate(:second))
    |> unique_constraint([:user_id, :tenant_id])
  end

  @doc """
  Changeset for creating an auto-approved association (e.g., first admin user).
  """
  @spec auto_approve_changeset(t(), map()) :: Ecto.Changeset.t()
  def auto_approve_changeset(user_tenant, attrs) do
    now = DateTime.utc_now() |> DateTime.truncate(:second)

    user_tenant
    |> cast(attrs, [:user_id, :tenant_id, :role])
    |> validate_required([:user_id, :tenant_id])
    |> validate_inclusion(:role, @valid_roles)
    |> put_change(:status, "approved")
    |> put_change(:requested_at, now)
    |> put_change(:approved_at, now)
    |> unique_constraint([:user_id, :tenant_id])
  end

  @doc """
  Changeset for approving a pending user-tenant association.
  """
  @spec approve_changeset(t(), map()) :: Ecto.Changeset.t()
  def approve_changeset(user_tenant, attrs) do
    user_tenant
    |> cast(attrs, [:approved_by])
    |> validate_required([:approved_by])
    |> put_change(:status, "approved")
    |> put_change(:approved_at, DateTime.utc_now() |> DateTime.truncate(:second))
    |> validate_pending_status()
  end

  @doc """
  Changeset for rejecting a pending user-tenant association.
  """
  @spec reject_changeset(t(), map()) :: Ecto.Changeset.t()
  def reject_changeset(user_tenant, attrs) do
    user_tenant
    |> cast(attrs, [:approved_by, :rejection_reason])
    |> validate_required([:approved_by, :rejection_reason])
    |> put_change(:status, "rejected")
    |> put_change(:approved_at, DateTime.utc_now() |> DateTime.truncate(:second))
    |> validate_pending_status()
  end

  @doc """
  Returns all valid roles for user-tenant associations.
  """
  @spec valid_roles() :: [String.t()]
  def valid_roles, do: @valid_roles

  @doc """
  Returns all valid statuses for user-tenant associations.
  """
  @spec valid_statuses() :: [String.t()]
  def valid_statuses, do: @valid_statuses

  # Private validation functions
  defp validate_pending_status(changeset) do
    # Get the status from the original data, not the changeset changes
    original_status = changeset.data.status
    case original_status do
      "pending" -> changeset
      status -> add_error(changeset, :status, "must be pending, current status: #{status}")
    end
  end
end