Course: WEBD-201 Web Development Fundamentals
Instructor: Prof. <PERSON>
Organization: University ABC
School: School of Arts & Sciences
Department: Applied Technologies
Year: 2025
Semester: Spring 2025
Section: Section B

Course Description:
An introduction to modern web development covering both front-end and back-end technologies. Students will learn HTML5, CSS3, JavaScript, responsive design, and basic server-side programming.

Learning Objectives:
1. Create semantic HTML5 markup
2. Style web pages with CSS3 and responsive design principles
3. Add interactivity with JavaScript and DOM manipulation
4. Understand web accessibility standards
5. Deploy web applications to hosting platforms
6. Work with version control systems

Topics Covered:
- HTML5 Semantic Elements
- CSS3 Layout and Flexbox/Grid
- Responsive Web Design
- JavaScript Fundamentals
- DOM Manipulation and Events
- AJAX and Fetch API
- Web Accessibility (WCAG)
- Version Control with Git
- Introduction to Node.js
- Database Integration Basics

Prerequisites: Basic computer literacy

Required Software:
- Visual Studio Code
- Modern web browser (Chrome, Firefox, Safari)
- Git and GitHub account

Grading:
- Weekly Lab Assignments: 30%
- Projects: 50%
- Participation: 20%

Major Projects:
1. Personal Portfolio Website
2. Interactive Web Application
3. Team Capstone Project

Schedule:
Week 1-2: HTML5 Fundamentals
Week 3-4: CSS3 and Layout
Week 5-6: Responsive Design
Week 7-8: JavaScript Basics
Week 9-10: DOM and Events
Week 11-12: AJAX and APIs
Week 13-14: Final Projects
Week 15-16: Presentations and Review