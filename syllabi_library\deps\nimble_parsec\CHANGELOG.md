# Changelog for NimbleParsec

## v1.4.2 (2025-01-21)

### Enhancements

  * Remove more warnings on Elixir v1.18+

## v1.4.1 (2025-01-15)

### Enhancements

  * Remove warnings on Elixir v1.18+

## v1.4.0 (2023-11-08)

### Enhancements

  * Remove warnings and require Elixir v1.12

## v1.3.1 (2023-04-30)

### Bug fixes

  * Make error messages consistent across Elixir versions
  * Ensure `integer(max: ...)` also sets a minimum of 1
  * Do not lose context on `repeat_while`

## v1.3.0 (2023-03-27)

### Deprecations

  * Deprecate returning a `{acc, context}` from `quoted_post_traverse` and `quoted_pre_traverse`. Return `{rest, acc, context}` instead.

### Bug fixes

  * Avoid compile warnings with negative lookahead
  * Fix clause warning for integer combinator

## v1.2.3 (2022-02-28)

### Enhancements

  * Allow `:export_combinator` in `defparsec`

## v1.2.2 (2022-02-12)

### Enhancements

  * Allow `rest` to be returned from post_traverse/pre_traverse

## v1.2.1 (2022-01-26)

### Bug fixes

  * Improve typespecs

## v1.2.0 (2021-11-07)

### Enhancements

  * Add generator support to `NimbleParsec`: it now generates code that matches a combinator

### Bug fixes

  * Fix using `eventually` with constructs that can accumulate
  * Fix lookahead combinators running in reverse order
  * Raise on invalid `min` for integer

## v1.1.0 (2020-10-02)

### Enhancements

  * Add `defcombinator` and support for remote `parsec`. This allows larger parsers to be broken over multiple modules to speed up compilation by leveraging parallelism.

## v1.0.0 (2020-09-25)

### Enhancements

  * Allow `byte_offset` to be given to `:line`

## v0.6.0 (2020-05-25)

NimbleParsec v0.6.0 requires Elixir v1.6+.

### Bug fixes

  * Avoid unused variable warnings

## v0.5.3 (2019-12-11)

### Enhancements

  * Add `eventually/2` combinator

## v0.5.2 (2019-11-10)

### Enhancements

  * Optimize `repeat` with initial `lookahead`
  * Add `@spec` to `parsec/2`

## v0.5.1 (2019-08-04)

### Enhancements

  * Remove dialyzer warnings from emitted code
  * Optimize lookahead with choice
  * Support min: 0 on utf8_string/ascii_string
  * Remove compiler warnings in code generated by `mix nimble_parsec.compile`
  * Add `@spec` to combinators

## v0.5.0 (2018-12-12)

### Enhancements

  * Introduce `pre_traverse/3`
  * Introduce `lookahead/2` and `lookahead_not/2` which accepts combinators
  * Introduce `eos/1` (end of string combinator)

### Backwards incompatible changes

  * Deprecate `traverse/3` in favor of `post_traverse/3`
  * `repeat_until/3` has been removed in favor of `repeat/2` with `lookahead_not/2` (see `repeat_while/3` docs for an example)
  * `lookahead/2` with a call has been removed as it can be easily achieved with a `post_traverse/3`

## v0.4.0 (2018-08-10)

### Enhancements

  * Add `defcombinatorp` and clarify the role of `defparsecp`

## v0.3.2 (2018-06-02)

### Bug fixes

  * Fix compilation error for certain combinations of ascii_string/utf8_string + traverse
  * Respect the `label` construct when wrapping a `choice`

## v0.3.1 (2018-04-10)

### Bug fixes

  * Avoid exponential compilation time when line break is unknown

## v0.3.0 (2018-04-08)

### Enhancements

  * Add `unwrap`, `tag` and `unwrap_and_tag`
  * Add `nimble_parsec.compile` to compile parsers without a NimbleParsec dependency

## v0.2.2 (2018-03-08)

### Bug fix

  * Ensure multiple runtime traversals compose

## v0.2.1 (2018-03-04)

### Bug fix

  * Do not duplicate entries in integer/ascii_string/utf8_string

## v0.2.0 (2018-03-04)

### Enhancements

  * Support parser combinator contexts and return them in ok/error tuples
  * Add `ascii_string/3` and `utf_string/3` combinators
  * Add `lookahead/2` combinator
  * Rename `literal/2` to `string/2`
  * Add `line/2` and `byte_offset/2` combinators
  * The MFA given to `repeat_while/3` now receives the accumulator, context, line, and byte_offset values and must return `{:cont, context} | {:halt, context}`
  * The MFA given to `quoted_repeat_while/3` now receives the accumulator, context, line, and byte_offset values and must return `{:cont, context} | {:halt, context}`
  * The MFA given to `traverse/3` now receives the rest, accumulator, context, line, and byte_offset values and must return `{acc, context} | {:error, reason}`
  * The MFA given to `quoted_traverse/3` now receives the ASTs for the rest, accumulator, context, line, and byte_offset and must return `{acc, context} | {:error, reason}`
  * Instead of `line` as a positive integer, we now track `{line, line_offset}` where `line` is the same as before and `line_offset` is the byte offset after the new line
  * Instead of `column` as a positive integer, we now track `byte_offset` as a non-negative integer

## v0.1.0 (2018-03-02)

  * First release.
